import React from 'react';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { AdminDepositData } from '../types';
import { formatNumber } from '@/utils/dateUtils';
import { TaxSummaryTable } from '@/features/shared/deposit/components/TaxSummaryTable';
import { PaymentBreakdownTable } from '@/features/shared/deposit/components/PaymentBreakdownTable';
import { StoreSummaryTable } from '@/features/shared/deposit/components/StoreSummaryTable';

interface DepositTablesProps {
  data: AdminDepositData;
  areaFilters: any;
  transferDate: string;
  switchLayoutDate: string;
}

export const DepositTables: React.FC<DepositTablesProps> = ({
  data,
  areaFilters,
  transferDate,
  switchLayoutDate
}) => {


  const getDepositDetailLink = () => {
    const area = areaFilters.areaSelected === 'all' ? '' : areaFilters.areaSelected;
    const subArea = areaFilters.subAreaSelected === 'all' ? '' : areaFilters.subAreaSelected;
    const merchant = areaFilters.merchantSelected === 'all' ? '' : areaFilters.merchantSelected;

    return `/admin-store/deposit/detail/${transferDate}?area=${area}&subArea=${subArea}&merchant=${merchant}`;
  };

  return (
    <div className="space-y-2">
      {/* Summary Table */}
      <StoreSummaryTable
        merchantPayments={data.agxMerchantPayments || []}
        transferDate={transferDate}
        isAdminView={true}
        detailLink={getDepositDetailLink()}
      />

      {/* Merchant Payments Table */}
      <Card className='border-0 shadow-none text-[#6F6F6E] rounded-none border-b border-[#6F6F6E]'>
        <CardHeader className='p-4'>
          <CardTitle className="text-2xl font-medium">決済種別ごとのデータ</CardTitle>
        </CardHeader>
        <CardContent className='p-0'>
          <div className="bg-white w-full max-w-[calc(100vw-44px)] overflow-x-auto text-xl">
            <Table className="min-w-[1170px]">
              <TableHeader>
                <TableRow className="border-none">
                  <TableHead className="py-3 px-2 text-center bg-white text-[#6F6F6E] font-normal text-xl">
                    <span className="w-[98%] inline-block border-b border-[#6F6F6E] px-2 py-3">加盟店番号</span>
                  </TableHead>
                  <TableHead className="py-3 px-2 text-center bg-white text-[#6F6F6E] font-normal text-xl">
                    <span className="w-[98%] inline-block border-b border-[#6F6F6E] px-2 py-3">加盟店名</span>
                  </TableHead>
                  <TableHead className="py-3 px-2 text-center bg-white text-[#6F6F6E] font-normal text-xl">
                    <span className="w-[98%] inline-block border-b border-[#6F6F6E] px-2 py-3">売上件数</span>
                  </TableHead>
                  <TableHead className="py-3 px-2 text-center bg-white text-[#6F6F6E] font-normal text-xl">
                    <span className="w-[98%] inline-block border-b border-[#6F6F6E] px-2 py-3">売上金額 </span>
                  </TableHead>
                  <TableHead className="py-3 px-2 text-center bg-white text-[#6F6F6E] font-normal text-xl">
                    <span className="w-[98%] inline-block border-b border-[#6F6F6E] px-2 py-3">手数料額 </span>
                  </TableHead>
                  <TableHead className="py-3 px-2 text-center bg-white text-[#6F6F6E] font-normal text-xl">
                    <span className="w-[98%] inline-block border-b border-[#6F6F6E] px-2 py-3">（内消費税）</span>
                  </TableHead>
                  <TableHead className="py-3 px-2 text-center bg-white text-[#6F6F6E] font-normal text-xl">
                    <span className="w-[98%] inline-block border-b border-[#6F6F6E] px-2 py-3">振込額 </span>
                  </TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {data.agxMerchantPayments.slice(0, -1).map((item, index) => (
                  <TableRow key={index} className="border-0">
                    <TableCell className="py-3 px-2 text-center bg-white text-xl">
                      {item.merchantNo}
                    </TableCell>
                    <TableCell className="py-3 px-2 text-left bg-white text-xl">
                      {item.storeName}
                    </TableCell>
                    <TableCell className="py-3 px-2 text-center bg-white text-xl">
                      {formatNumber(item.numberOfSales)}件
                    </TableCell>
                    <TableCell className="py-3 px-2 text-center bg-white text-xl">
                      {formatNumber(item.salesAmount)}円
                    </TableCell>
                    <TableCell className="py-3 px-2 text-center bg-white text-xl">
                      {formatNumber(item.totalFee)}円
                    </TableCell>
                    <TableCell className="py-3 px-2 text-center bg-white text-xl">
                      ({formatNumber(item.sumTax)})円
                    </TableCell>
                    <TableCell className="py-3 px-2 text-center bg-white text-xl">
                      {formatNumber(item.paymentAmount)}円
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>

      {/* Deposit Breakdown Table */}
      <PaymentBreakdownTable
        paymentBreakdowns={data.agxPaymentBreakdowns || []}
        transferDate={transferDate}
        isAdminView={true}
      />


      {/* Tax Summary Table - Only show for dates after switchLayoutDate */}
      <TaxSummaryTable
        data={{
          subTotalNonTax: data.subTotalNonTax,
          subTotalInclTax10: data.subTotalInclTax10,
          subTotalConsumptionTax: data.subTotalConsumptionTax,
          subTotalTaxIncl: data.subTotalTaxIncl
        }}
        transferDate={transferDate}
        switchLayoutDate={switchLayoutDate}
      />
    </div>
  );
};
